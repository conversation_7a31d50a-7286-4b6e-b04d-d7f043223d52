[{"__type__": "cc.animation.AnimationGraph", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_layers": [{"__id__": 1}], "_variables": {"isMove": {"__id__": 39}, "isCombat": {"__id__": 40}, "atkSpd": {"__id__": 41}}}, {"__type__": "cc.animation.Layer", "_stateMachine": {"__id__": 2}, "name": "", "weight": 1, "mask": null, "additive": false, "_stashes": {}}, {"__type__": "cc.animation.StateMachine", "__editorExtras__": {"name": "", "id": "17517152592000.38520653456381226", "clone": null, "viewport": {"scale": 1.6600000000000006, "top": -35.52842594429553, "left": -37.2980985405677}}, "_states": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}, {"__id__": 6}, {"__id__": 10}, {"__id__": 14}], "_transitions": [{"__id__": 18}, {"__id__": 19}, {"__id__": 24}, {"__id__": 29}, {"__id__": 34}], "_entryState": {"__id__": 3}, "_exitState": {"__id__": 4}, "_anyState": {"__id__": 5}}, {"__type__": "cc.animation.State", "__editorExtras__": {"name": "", "id": "17517152592000.3937822239992592", "clone": null, "centerX": -125, "centerY": 1}, "name": "Entry"}, {"__type__": "cc.animation.State", "__editorExtras__": {"name": "", "id": "17517152592000.03512228219149072", "clone": null, "centerX": 125, "centerY": 0}, "name": "Exit"}, {"__type__": "cc.animation.State", "__editorExtras__": {"name": "", "id": "17517152592000.08740836117702311", "clone": null, "centerX": 125, "centerY": 0}, "name": "Any"}, {"__type__": "cc.animation.Motion", "__editorExtras__": {"name": "Idle", "id": "17517723489760.025905215916672386", "clone": null, "centerX": -4.849917081260287, "centerY": -81.31107483416253}, "name": "Idle", "_components": [], "motion": {"__id__": 7}, "speed": 1, "speedMultiplier": "", "speedMultiplierEnabled": false, "transitionInEventBinding": {"__id__": 8}, "transitionOutEventBinding": {"__id__": 9}}, {"__type__": "cc.animation.ClipMotion", "__editorExtras__": {"name": "", "id": "17517723489820.7023352672680476", "clone": null}, "clip": {"__uuid__": "5b65eb77-92cc-409d-bb2b-fc984064f2d8@73b7f", "__expectedType__": "cc.AnimationClip"}}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.Motion", "__editorExtras__": {"name": "Run", "id": "17517826483460.537110620538962", "clone": null, "centerX": -3.41270512344704, "centerY": 88.45289519860955}, "name": "Run", "_components": [], "motion": {"__id__": 11}, "speed": 1, "speedMultiplier": "", "speedMultiplierEnabled": false, "transitionInEventBinding": {"__id__": 12}, "transitionOutEventBinding": {"__id__": 13}}, {"__type__": "cc.animation.ClipMotion", "__editorExtras__": {"name": "", "id": "17517826483660.5032030374771204", "clone": null}, "clip": {"__uuid__": "b259685b-e0a2-4950-bd5f-42c7f5206a1e@73b7f", "__expectedType__": "cc.AnimationClip"}}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.Motion", "__editorExtras__": {"name": "Attack", "id": "d800b146-cbd3-4a99-a591-5888c1cc56f5", "clone": null, "centerX": -3.846781949006413, "centerY": 0.4445544158364214}, "name": "Attack", "_components": [], "motion": {"__id__": 15}, "speed": 1, "speedMultiplier": "atkSpd", "speedMultiplierEnabled": true, "transitionInEventBinding": {"__id__": 16}, "transitionOutEventBinding": {"__id__": 17}}, {"__type__": "cc.animation.ClipMotion", "__editorExtras__": {"name": "", "id": "458bde5e-dd98-4fd4-9efd-1e6730467d1b", "clone": null}, "clip": {"__uuid__": "655528a7-807d-47ff-84c5-493f9e3bc03c@090ca", "__expectedType__": "cc.AnimationClip"}}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.Transition", "__editorExtras__": {"name": "", "id": "17517826483660.9553253640220418", "clone": null}, "from": {"__id__": 3}, "to": {"__id__": 6}, "conditions": []}, {"__type__": "cc.animation.AnimationTransition", "__editorExtras__": null, "from": {"__id__": 6}, "to": {"__id__": 14}, "conditions": [{"__id__": 20}], "destinationStart": 0, "relativeDestinationStart": false, "startEventBinding": {"__id__": 22}, "endEventBinding": {"__id__": 23}, "duration": 0.3, "relativeDuration": false, "exitConditionEnabled": false, "_exitCondition": 1}, {"__type__": "cc.animation.UnaryCondition", "operator": 0, "operand": {"__id__": 21}}, {"__type__": "cc.animation.BindableBoolean", "variable": "isCombat", "value": false}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.AnimationTransition", "__editorExtras__": null, "from": {"__id__": 14}, "to": {"__id__": 10}, "conditions": [{"__id__": 25}], "destinationStart": 0, "relativeDestinationStart": false, "startEventBinding": {"__id__": 27}, "endEventBinding": {"__id__": 28}, "duration": 0.3, "relativeDuration": false, "exitConditionEnabled": false, "_exitCondition": 1}, {"__type__": "cc.animation.UnaryCondition", "operator": 0, "operand": {"__id__": 26}}, {"__type__": "cc.animation.BindableBoolean", "variable": "isMove", "value": false}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.AnimationTransition", "__editorExtras__": null, "from": {"__id__": 10}, "to": {"__id__": 14}, "conditions": [{"__id__": 30}], "destinationStart": 0, "relativeDestinationStart": false, "startEventBinding": {"__id__": 32}, "endEventBinding": {"__id__": 33}, "duration": 0.3, "relativeDuration": false, "exitConditionEnabled": false, "_exitCondition": 1}, {"__type__": "cc.animation.UnaryCondition", "operator": 1, "operand": {"__id__": 31}}, {"__type__": "cc.animation.BindableBoolean", "variable": "isMove", "value": false}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.AnimationTransition", "__editorExtras__": null, "from": {"__id__": 14}, "to": {"__id__": 6}, "conditions": [{"__id__": 35}], "destinationStart": 0, "relativeDestinationStart": false, "startEventBinding": {"__id__": 37}, "endEventBinding": {"__id__": 38}, "duration": 0.3, "relativeDuration": false, "exitConditionEnabled": false, "_exitCondition": 1}, {"__type__": "cc.animation.UnaryCondition", "operator": 1, "operand": {"__id__": 36}}, {"__type__": "cc.animation.BindableBoolean", "variable": "isCombat", "value": false}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.AnimationGraphEventBinding", "methodName": ""}, {"__type__": "cc.animation.PlainVariable", "_type": 1, "_value": false}, {"__type__": "cc.animation.PlainVariable", "_type": 1, "_value": false}, {"__type__": "cc.animation.PlainVariable", "_type": 0, "_value": 1}]