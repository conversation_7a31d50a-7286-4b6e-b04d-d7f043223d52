export enum CharacterState {
    Idle,
    Attack,
    Move
}

export interface ICharacterState {
    onEnter(): void;
    onExit(): void;
    onUpdate(deltaTime: number): void;
}

export class StateMachine {
    private currentState: ICharacterState;

    public initialize(startingState: ICharacterState): void {
        this.currentState = startingState;
        this.currentState.onEnter();
    }

    public changeState(newState: ICharacterState): void {
        this.currentState.onExit();
        this.currentState = newState;
        this.currentState.onEnter();
    }

    public update(deltaTime: number): void {
        this.currentState.onUpdate(deltaTime);
    }
}