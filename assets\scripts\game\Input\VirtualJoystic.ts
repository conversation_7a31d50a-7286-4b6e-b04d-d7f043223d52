import { _decorator, Component, Node, Vec3, input, Input, EventMouse, Vec2, EventTouch, CCFloat, UITransform, Canvas, find } from "cc";
import { IInput } from "./IInput";
const { ccclass, property } = _decorator;

@ccclass("VirtualJoystic")
export class Virtual<PERSON>oystic extends Component implements IInput {
    @property(CCFloat) private maxDistance = 10;
    @property(Node) private knob: Node;

    #isUsingJoystic = false;
    #defaultPosition: Vec2 = new Vec2();
    #canvasUITransform: UITransform = null;

    public init(canvas: Canvas): void {
        // Get Canvas UITransform for coordinate conversion
        if (canvas) {
            this.#canvasUITransform = canvas.getComponent(UITransform);
        }

        input.on(Input.EventType.MOUSE_DOWN, this.activateMouseJoystic, this);
        input.on(Input.EventType.MOUSE_UP, this.deactivateJoystic, this);
        input.on(Input.EventType.MOUSE_MOVE, this.moveKnobMouse, this);

        input.on(Input.EventType.TOUCH_START, this.activateTouchJoystic, this);
        input.on(Input.EventType.TOUCH_END, this.deactivateJoystic, this);
        input.on(Input.EventType.TOUCH_MOVE, this.moveKnobTouch, this);

        this.deactivateJoystic();
    }

    public getAxis(): Vec2 {
        if (this.#isUsingJoystic) {
            return new Vec2(this.knob.position.x / this.maxDistance, this.knob.position.y / this.maxDistance);
        } else {
            return new Vec2();
        }
    }

    private activateTouchJoystic(e: EventTouch): void {
        this.activateJoystic(e.getUILocation());
    }

    private activateMouseJoystic(e: EventMouse): void {
        // console.log("Mouse location:", e.getLocation());
        // console.log("Mouse UI location:", e.getUILocation());
        this.activateJoystic(e.getUILocation());
    }

    private convertUIToLocalPosition(uiPos: Vec2): Vec2 {
        if (!this.#canvasUITransform) {
            return uiPos;
        }

        // Convert UI position to local position relative to this node's parent
        const worldPos = new Vec3(uiPos.x, uiPos.y, 0);
        const localPos = new Vec3();

        // Convert to parent's local space (since joystick is child of Canvas)
        this.node.parent.getComponent(UITransform).convertToNodeSpaceAR(worldPos, localPos);

        return new Vec2(localPos.x, localPos.y);
    }

    private activateJoystic(location: Vec2): void {
        this.#isUsingJoystic = true;
        this.node.active = true;

        // Convert UI coordinates to local coordinates
        const localPosition = this.convertUIToLocalPosition(location);
        this.#defaultPosition = localPosition;

        this.node.setPosition(new Vec3(this.#defaultPosition.x, this.#defaultPosition.y, 0));
        this.knob.position = new Vec3();
    }

    private deactivateJoystic(): void {
        this.#isUsingJoystic = false;
        this.node.active = false;
    }

    private moveKnobTouch(e: EventTouch): void {
        this.moveKnob(e.getUILocation());
    }

    private moveKnobMouse(e: EventMouse): void {
        this.moveKnob(e.getUILocation());
    }

    private moveKnob(location: Vec2): void {
        if (!this.#isUsingJoystic) return;

        // Convert UI coordinates to local coordinates
        const localPosition = this.convertUIToLocalPosition(location);
        const posDelta: Vec2 = localPosition.subtract(this.#defaultPosition);
        let x: number = posDelta.x;
        let y: number = posDelta.y;

        const length: number = Math.sqrt(posDelta.x ** 2 + posDelta.y ** 2);
        if (this.maxDistance < length) {
            const multiplier: number = this.maxDistance / length;

            x *= multiplier;
            y *= multiplier;
        }

        this.knob.position = new Vec3(x, y, 0);
    }
}
