import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('CharacterVisual')
export class CharacterVisual extends Component {

    private facingRight = true;

    private setFacingRight(facingRight: boolean): void {
        if (this.facingRight === facingRight) {
            return;
        }

        this.facingRight = facingRight;

        const scale = this.node.getScale();
        scale.x = Math.abs(scale.x) * (facingRight ? 1 : -1);
        this.node.setScale(scale);

        console.log("FacingRight: " + this.node.scale.x);
    }

    public get FacingRight(): boolean {
        return this.facingRight;
    }

    public set FacingRight(value: boolean) {
        this.setFacingRight(value);
    }

}


