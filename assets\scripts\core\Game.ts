import { _decorator, Canvas, Component, Node, KeyCode, Button } from 'cc';
import { VirtualJoystic } from '../game/Input/VirtualJoystic';
import { KeyboardInput } from '../game/Input/KeyboardInput';
import { MultiInput } from '../game/Input/MultiInput';
import { CharacterController } from '../character/CharacterController';
const { ccclass, property } = _decorator;

@ccclass('Game')
export class Game extends Component {
    @property(Canvas) private canvas: Canvas = null;
    @property(VirtualJoystic) private virtualJoystic: VirtualJoystic = null;
    @property(CharacterController) private player: CharacterController = null;
    @property(CharacterController) private enemy: CharacterController = null;

    @property(Button) private buttonStart: Button = null;
    @property(Button) private buttonStop: Button = null;

    protected start(): void {
        this.setup();

        this.buttonStart.node.on(Button.EventType.CLICK, () => {
            this.player.enterCombatMode();
            this.player.CharacterAttack.setTarget(this.enemy.Health);
            // this.enemy.enterCombatMode();
        });

        this.buttonStop.node.on(Button.EventType.CLICK, () => {
            this.player.exitCombatMode();
            // this.enemy.exitCombatMode();
        });
    }

    update(deltaTime: number) {
        this.player.gameTick(deltaTime);
        // this.enemy.gameTick(deltaTime);
    }


    private setup(): void {
        this.virtualJoystic.init(this.canvas);

        const wasd = new KeyboardInput(KeyCode.KEY_W, KeyCode.KEY_S, KeyCode.KEY_A, KeyCode.KEY_D);
        const multiInput: MultiInput = new MultiInput([this.virtualJoystic, wasd]);

        this.player.init(multiInput);
        this.enemy.init(multiInput);
    }


}


