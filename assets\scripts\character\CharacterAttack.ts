import { _decorator, Component, instantiate, Node, Prefab } from 'cc';
import { GameTimer } from '../services/GameTimer';
import { Health } from './Health';
import { CharacterAnimator } from './CharacterAnimator';
import { Projectile } from '../game/ProjectileComponent/Projectile';
const { ccclass, property } = _decorator;

@ccclass('CharacterAttack')
export class CharacterAttack extends Component {

    @property(Prefab) private projectilePrefab: Prefab = null;
    @property(Node) private projectileVisual: Node = null;
    

    private strikeTimer: GameTimer;
    private damage: number;

    private target: Health = null;
    private currentProjectile: Node = null;

    public init(strikeDelay: number, damage: number): void {
        this.strikeTimer = new GameTimer(strikeDelay);
        this.damage = damage;

        this.projectileVisual.active = false;
    }

    public gameTick(deltaTime: number): void {

        if (this.target === null || !this.target.IsAlive) {
            return;
        }

        this.strikeTimer.gameTick(deltaTime);
        if (this.strikeTimer.tryFinishPeriod()) {
            this.strike();
        }
    }

    private strike(): void {

    }

    public setTarget(target: Health): void {
        this.target = target;
    }

    public get Target(): Health {
        return this.target;
    }

    public subscribeToSignals(animator: CharacterAnimator): void {
        animator.onStartFire.on(this.startFire, this);
        animator.onFire.on(this.fire, this);
    }

    private startFire(): void {
        this.projectileVisual.active = true;
        
        
    }

    private fire(): void {

        const pos = this.projectileVisual.worldPosition.clone();
        const rotate = this.projectileVisual.worldRotation.clone();

        this.projectileVisual.active = false;

        this.currentProjectile = instantiate(this.projectilePrefab);
        this.currentProjectile.parent = this.node;

        this.currentProjectile.setWorldPosition(pos);
        this.currentProjectile.setWorldRotation(rotate);
        this.currentProjectile.getComponent(Projectile).setTarget(this.target);
        
    }

    public showProjectileVisual(): void {
       
    }

    public hideProjectileVisual(): void {
       
    }
    
}


