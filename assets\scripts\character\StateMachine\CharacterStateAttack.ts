import { Vec2 } from "cc";
import { CharacterController } from "../CharacterController";
import { ICharacterState } from "./StateMachine";

export class CharacterStateAttack implements ICharacterState {
    private controller: CharacterController;

    public constructor(controller: CharacterController) {
        this.controller = controller;
    }

    onEnter(): void {
        this.controller.CharacterAnimator.IsCombat = true;
        this.controller.setFacingRight(this.controller.IsPlayer);
        this.controller.CharacterAnimator.setAttackSpeed(this.controller.CharacterData.atkSpeed);
        
    }

    onExit(): void {

    }

    onUpdate(deltaTime: number): void {
        const movement: Vec2 = new Vec2(this.controller.Input.getAxis().x, 0);

        if (!movement.equals(Vec2.ZERO)) {
            this.controller.changeState(this.controller.moveState);
            return;
        }
    }
}