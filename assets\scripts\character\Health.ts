import { _decorator, Component, Node, Vec3 } from 'cc';
import { Signal } from '../eventSystem/Signal';
import { ISignal } from '../eventSystem/ISignal';
const { ccclass, property } = _decorator;

@ccclass('Health')
export class Health {
    private healthPoints: number;
    private maxHealthPoints: number;
    private healthPointsChangeEvent: Signal<number> = new Signal<number>();

    private node: Node;

    public constructor(maxHealth: number, characterNode: Node) {
        this.maxHealthPoints = maxHealth;
        this.healthPoints = maxHealth;
        this.node = characterNode;
    }

    public get IsAlive(): boolean {
        return 0 < this.healthPoints;
    }

    public get HealthPoints(): number {
        return this.healthPoints;
    }
    public get MaxHealthPoints(): number {
        return this.maxHealthPoints;
    }

    public get HealthPointsChangeEvent(): ISignal<number> {
        return this.healthPointsChangeEvent;
    }

    public heal(points: number): void {
        this.healthPoints = Math.min(this.maxHealthPoints, this.healthPoints + points);
        this.healthPointsChangeEvent.trigger(points);
    }

    public damage(points: number): void {
        this.healthPoints -= points;
        this.healthPointsChangeEvent.trigger(-points);
    }

    public setMaxHealth(maxHealth: number): void {
        this.maxHealthPoints = maxHealth;
    }

    public get WorldPosition(): Vec3 {
        return this.node.worldPosition;
    }
}


