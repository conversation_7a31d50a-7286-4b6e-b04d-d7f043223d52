import { Vec2, Vec3 } from "cc";
import { CharacterController } from "../CharacterController";
import { ICharacterState } from "./StateMachine";

export class CharacterStateMove implements ICharacterState {
    private controller: CharacterController;
    private moveSpeed = 2;

    public constructor(controller: CharacterController, moveSpeed: number) {
        this.controller = controller;
        this.moveSpeed = moveSpeed;
    }

    onEnter(): void {
        this.controller.CharacterAnimator.IsMove = true;
    }

    onExit(): void {
        this.controller.CharacterAnimator.IsMove = false;
    }

    onUpdate(deltaTime: number): void {
        const movement: Vec2 = new Vec2(this.controller.Input.getAxis().x, 0);

        if (!movement.equals(Vec2.ZERO)) {

            this.controller.setFacingRight(movement.x > 0);

            movement.x *= deltaTime * this.moveSpeed;
            movement.y *= deltaTime * this.moveSpeed;

            const newPosition: Vec3 = this.controller.node.worldPosition;
            newPosition.x += movement.x;
            newPosition.y += movement.y;

            this.controller.node.setWorldPosition(newPosition);

            return;
        }
        else {
            this.controller.changeState(this.controller.attackState);
            return;
        }
    }
}