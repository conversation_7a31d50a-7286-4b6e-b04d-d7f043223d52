import { _decorator, Component, Node, Vec3, CCFloat, Quat, math, v3 } from 'cc';
import { Health } from '../../character/Health';


const { ccclass, property } = _decorator;

@ccclass('Projectile')
export class Projectile extends Component {
    
    @property(CCFloat) private speed = 10;
    @property(CCFloat) private baseGravity = -20;
    @property(CCFloat) private minGravity = -30; // Gravity nhỏ (giá trị âm lớn) cho khoảng cách xa (góc cao)
    @property(CCFloat) private maxGravity = -10; // Gravity lớn (giá trị âm nhỏ) cho khoảng cách gần (góc thấp)
    @property(CCFloat) private minDistance = 2;  // Khoảng cách tối thiểu
    @property(CCFloat) private maxDistance = 10; // Khoảng cách tối đa

    private target: Health = null;
    private velocity: Vec3 = new Vec3();
    private startPos: Vec3 = new Vec3();
    private time: number = 0;
    private isFlying: boolean = false;
    private currentGravity: number = -20; // Gravity được tính toán dựa trên khoảng cách

    public setTarget(target: Health): void {
        this.target = target;
        this.startPos = this.node.worldPosition.clone();
        const endPos = target.WorldPosition.clone();

        // Tính khoảng cách để điều chỉnh gravity
        const distance = Vec3.distance(this.startPos, endPos);
        this.calculateGravityByDistance(distance);

        // Tính toán thời gian bay (có thể điều chỉnh)
        const flightTime = 1.0;

        // Tính vận tốc ban đầu cho từng trục
        this.velocity.x = (endPos.x - this.startPos.x) / flightTime;
        this.velocity.z = (endPos.z - this.startPos.z) / flightTime;
        this.velocity.y = (endPos.y - this.startPos.y - 0.5 * this.currentGravity * flightTime * flightTime) / flightTime;

        this.time = 0;
        this.isFlying = true;
    }

    public get Target(): Health {
        return this.target;
    }

    /**
     * Tính toán gravity dựa trên khoảng cách
     * Khoảng cách càng xa thì gravity càng nhỏ (giá trị âm lớn hơn) -> góc bay càng cao
     * Khoảng cách càng gần thì gravity càng lớn (giá trị âm nhỏ hơn) -> góc bay càng thấp
     */
    private calculateGravityByDistance(distance: number): void {
        // Clamp khoảng cách trong khoảng min-max
        const clampedDistance = math.clamp(distance, this.minDistance, this.maxDistance);

        // Tính tỷ lệ (0 = gần nhất, 1 = xa nhất)
        const ratio = (clampedDistance - this.minDistance) / (this.maxDistance - this.minDistance);

        // Interpolate gravity:
        // khoảng cách gần (ratio = 0) -> gravity lớn (maxGravity = -10) -> góc thấp
        // khoảng cách xa (ratio = 1) -> gravity nhỏ (minGravity = -30) -> góc cao
        this.currentGravity = math.lerp(this.maxGravity, this.minGravity, ratio);
    }

    update(dt: number) {
        if (!this.isFlying) return;

        this.time += dt;

        // Cập nhật vị trí theo quỹ đạo parabol trên mặt phẳng x, y, z = 0
        const displacement = new Vec3(
            this.velocity.x * this.time,
            this.velocity.y * this.time + 0.5 * this.currentGravity * this.time * this.time,
            0 // z luôn bằng 0
        );
        const newPos = this.startPos.clone().add(displacement);
        newPos.z = 0; // Đảm bảo z luôn bằng 0
        this.node.setWorldPosition(newPos);

        // Lấy vận tốc hiện tại trên mặt phẳng x, y
        const vx = this.velocity.x;
        const vy = this.velocity.y + this.currentGravity * this.time;

        // Tính góc xoay quanh trục z
        const angle = Math.atan2(vy, vx); // radian

        // Tạo quaternion từ góc này quanh trục z
        const quat = new Quat();
        Quat.fromAxisAngle(quat, new Vec3(0, 0, 1), angle);
        this.node.setWorldRotation(quat);

        // Kiểm tra nếu đã đến gần target thì dừng lại
        if (this.target && Vec3.distance(newPos, this.target.WorldPosition) < 0.5) {
            this.isFlying = false;
            // TODO: Xử lý va chạm hoặc hiệu ứng tại đây
        }
    }
}