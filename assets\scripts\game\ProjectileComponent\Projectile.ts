import { _decorator, Component, Node, Vec3, CCFloat, Quat, math, v3 } from 'cc';
import { Health } from '../../character/Health';


const { ccclass, property } = _decorator;

@ccclass('Projectile')
export class Projectile extends Component {
    
    @property(CCFloat) private speed = 10;
    @property(CCFloat) private gravity = -20;

    private target: Health = null;
    private velocity: Vec3 = new Vec3();
    private startPos: Vec3 = new Vec3();
    private time: number = 0;
    private isFlying: boolean = false;

    public setTarget(target: Health): void {
        this.target = target;
        this.startPos = this.node.worldPosition.clone();
        const endPos = target.WorldPosition.clone();

        // Tính toán thời gian bay (có thể điều chỉnh)
        const flightTime = 1.0;

        // Tính vận tốc ban đầu cho từng trục
        this.velocity.x = (endPos.x - this.startPos.x) / flightTime;
        this.velocity.z = (endPos.z - this.startPos.z) / flightTime;
        this.velocity.y = (endPos.y - this.startPos.y - 0.5 * this.gravity * flightTime * flightTime) / flightTime;

        this.time = 0;
        this.isFlying = true;
    }

    public get Target(): Health {
        return this.target;
    }

    update(dt: number) {
        if (!this.isFlying) return;

        this.time += dt;

        // Cập nhật vị trí theo quỹ đạo parabol trên mặt phẳng x, y, z = 0
        const displacement = new Vec3(
            this.velocity.x * this.time,
            this.velocity.y * this.time + 0.5 * this.gravity * this.time * this.time,
            0 // z luôn bằng 0
        );
        const newPos = this.startPos.clone().add(displacement);
        newPos.z = 0; // Đảm bảo z luôn bằng 0
        this.node.setWorldPosition(newPos);

        // Lấy vận tốc hiện tại trên mặt phẳng x, y
        const vx = this.velocity.x;
        const vy = this.velocity.y + this.gravity * this.time;

        // Tính góc xoay quanh trục z
        const angle = Math.atan2(vy, vx); // radian

        // Tạo quaternion từ góc này quanh trục z
        const quat = new Quat();
        Quat.fromAxisAngle(quat, new Vec3(0, 0, 1), angle);
        this.node.setWorldRotation(quat);

        // Kiểm tra nếu đã đến gần target thì dừng lại
        if (this.target && Vec3.distance(newPos, this.target.WorldPosition) < 0.5) {
            this.isFlying = false;
            // TODO: Xử lý va chạm hoặc hiệu ứng tại đây
        }
    }
}