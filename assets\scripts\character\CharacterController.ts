import { _decorator, CCBoolean, Component, Node } from 'cc';
import { IInput } from '../game/Input/IInput';
import { CharacterAnimator } from './CharacterAnimator';
import { CharacterAttack } from './CharacterAttack';
import { CharacterStateAttack } from './StateMachine/CharacterStateAttack';
import { CharacterStateIdle } from './StateMachine/CharacterStateIdle';
import { CharacterStateMove } from './StateMachine/CharacterStateMove';
import { StateMachine, ICharacterState } from './StateMachine/StateMachine';
import { Health } from './Health';
const { ccclass, property } = _decorator;

@ccclass('CharacterController')
export class CharacterController extends Component {

    @property(CharacterAnimator) private animator: CharacterAnimator = null;
    @property(CCBoolean) private isPlayer = true;

    private attack: CharacterAttack = null;

    private stateMachine: StateMachine = new StateMachine();

    public idleState: CharacterStateIdle;
    public attackState: CharacterStateAttack;
    public moveState: CharacterStateMove;

    private isMoving = false;
    private isAttacking = false;
    private facingRight = true;

    private data: CharacterData;
    private input: IInput;
    private health: Health;

    onLoad() {
        this.attack = this.getComponent(CharacterAttack);
    }

    public init(input: IInput) {
        this.input = input;

        this.data = new CharacterData();

        this.attack.init(this.data.strikeDelay, this.data.damage);
        this.attack.subscribeToSignals(this.animator);
        this.health = new Health(this.data.maxHealth,this.node);

        this.setupStates();
        this.setFacingRight(this.isPlayer);
    }

    private setupStates(): void {
        this.idleState = new CharacterStateIdle(this);
        this.attackState = new CharacterStateAttack(this);
        this.moveState = new CharacterStateMove(this, this.data.moveSpeed);

        this.stateMachine.initialize(this.idleState);
    }

    public gameTick(deltaTime: number): void {
        this.stateMachine.update(deltaTime);
    }

    public get CharacterAnimator(): CharacterAnimator {
        return this.animator;
    }

    public get Input(): IInput {
        return this.input;
    }

    public get IsPlayer(): boolean {
        return this.isPlayer;
    }

    public get CharacterAttack(): CharacterAttack {
        return this.attack;
    }

    public changeState(newState: ICharacterState): void {
        this.stateMachine.changeState(newState);
    }

    public enterCombatMode(): void {
        this.changeState(this.attackState);
    }

    public exitCombatMode(): void {
        this.changeState(this.idleState);
    }

    public setFacingRight(facingRight: boolean): void {
        if (this.facingRight === facingRight) {
            return;
        }

        this.facingRight = facingRight;

        const scale = this.animator.node.getScale();
        scale.x = Math.abs(scale.x) * (facingRight ? 1 : -1);
        this.animator.node.setScale(scale);
    }

    public get Health(): Health {
        return this.health;
    }

    public get CharacterData(): CharacterData {
        return this.data;
    }

}


export class CharacterData {

    public moveSpeed = 2;
    public atkSpeed = 1;
    public maxHealth = 1000;
    public damage = 10;
    public strikeDelay = 1;
    
}