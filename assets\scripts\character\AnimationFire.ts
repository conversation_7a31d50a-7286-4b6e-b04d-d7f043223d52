import { _decorator, Component, Node, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('AnimationFire')
export class AnimationFire extends Component {
    @property(Node) private projectile: Node = null;


    protected start(): void {
        this.projectile.active = false;
    }

    public startFire(): void {
        this.projectile.active = true;
       
    }

    public fire(): void {
        this.projectile.active = false;
        console.log("fire");
    }
}


