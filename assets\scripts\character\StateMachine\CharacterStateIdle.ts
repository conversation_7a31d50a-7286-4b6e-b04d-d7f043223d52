
import { CharacterController } from "../CharacterController";
import { ICharacterState } from "./StateMachine";

export class CharacterStateIdle implements ICharacterState {
    private controller: CharacterController;

    public constructor(controller: CharacterController) {
        this.controller = controller;
    }

    onEnter(): void {
        this.controller.CharacterAnimator.IsCombat = false;
    }

    onExit(): void {
    }

    onUpdate(deltaTime: number): void {

    }
}
