import { _decorator, animation, Component, Node } from 'cc';
import { Signal } from '../eventSystem/Signal';
const { ccclass, property } = _decorator;

@ccclass('CharacterAnimator')
export class CharacterAnimator extends Component {
    @property(animation.AnimationController) private anim: animation.AnimationController = null;

    private isMove = false;
    private isCombat = false;

    public onStartFire: Signal = new Signal();
    public onFire: Signal = new Signal();


    public get IsMove(): boolean {
        return this.isMove;
    }

    public set IsMove(value: boolean) {
        this.isMove = value;
        this.anim.setValue("isMove", this.isMove);
    }

    public get IsCombat(): boolean {
        return this.isCombat;
    }

    public set IsCombat(value: boolean) {
        this.isCombat = value;
        this.anim.setValue("isCombat", this.isCombat);
    }

    public setAttackSpeed(value: number): void {
        this.anim.setValue("atkSpd", value);
    }


    public startFire(): void {
        this.onStartFire?.trigger();
    }

    public fire(): void {
        this.onFire?.trigger();
    }
}


